package eu.torvian.chatbot.server.data.dao.exposed

import arrow.core.Either
import arrow.core.left
import arrow.core.raise.catch
import arrow.core.raise.either
import arrow.core.right
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.server.data.dao.GroupOwnershipDao
import eu.torvian.chatbot.server.data.dao.error.GetOwnerError
import eu.torvian.chatbot.server.data.dao.error.SetOwnerError
import eu.torvian.chatbot.server.data.tables.ChatGroupOwnersTable
import eu.torvian.chatbot.server.data.tables.ChatGroupTable
import eu.torvian.chatbot.server.data.tables.mappers.toChatGroup
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.*

/**
 * Exposed implementation of the [GroupOwnershipDao].
 */
class GroupOwnershipDaoExposed(
    private val transactionScope: TransactionScope
) : GroupOwnershipDao {

    override suspend fun getAllGroupsForUser(userId: Long): List<ChatGroup> =
        transactionScope.transaction {
            ChatGroupTable
                .join(
                    ChatGroupOwnersTable,
                    JoinType.INNER,
                    additionalConstraint = { ChatGroupTable.id eq ChatGroupOwnersTable.groupId }
                )
                .selectAll()
                .where { ChatGroupOwnersTable.userId eq userId }
                .orderBy(ChatGroupTable.name to SortOrder.ASC)
                .map { it.toChatGroup() }
        }

    override suspend fun getOwner(groupId: Long): Either<GetOwnerError, Long> =
        transactionScope.transaction {
            ChatGroupOwnersTable
                .selectAll()
                .where { ChatGroupOwnersTable.groupId eq groupId }
                .singleOrNull()
                ?.let { it[ChatGroupOwnersTable.userId].value }
                ?.right()
                ?: GetOwnerError.ResourceNotFound(groupId.toString()).left()
        }

    override suspend fun setOwner(groupId: Long, userId: Long): Either<SetOwnerError, Unit> =
        transactionScope.transaction {
            either {
                catch({
                    ChatGroupOwnersTable.insert {
                        it[ChatGroupOwnersTable.groupId] = groupId
                        it[ChatGroupOwnersTable.userId] = userId
                    }
                    Unit
                }) { e: ExposedSQLException ->
                    when {
                        e.isForeignKeyViolation() -> 
                            raise(SetOwnerError.ForeignKeyViolation(groupId.toString(), userId))
                        e.isUniqueConstraintViolation() -> 
                            raise(SetOwnerError.AlreadyOwned)
                        else -> throw e
                    }
                }
            }
        }
}
